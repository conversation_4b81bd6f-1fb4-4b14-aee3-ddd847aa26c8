# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
**/node_modules/
/node_modules

# next.js
.next/
**/.next/
/out/
**/out/

# production
/build
build/
**/build/
dist/
**/dist/
out/
**/out/

# temporary upload files (Google Drive sync)
.tmp.driveupload/
**/.tmp.driveupload/
tmp.driveupload/
**/tmp.driveupload/
.tmp.drivedownload/
**/.tmp.drivedownload/
tmp.drivedownload/
**/tmp.drivedownload/

# IDE and tooling
archon/
/archon/
*archon/*


# logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
**/*.log

# env files
.env*
mcp.json

# typescript
*.tsbuildinfo
next-env.d.ts

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Security: Deployment files with secrets
*DEPLOYMENT*GUIDE*
*deployment*guide*
*DEPLOYMENT*STATUS*
*deployment*status*
*.secrets.*
*secrets*
*.credentials.*
*credentials*

# Security: Prevent any files with common API key patterns
*api-keys*
*api_keys*
*access-tokens*
*access_tokens*
# Sentry Config File
.env.sentry-build-plugin

# Backup Files Prevention
*.backup
*.nuclear_backup
*.corrupted
*.removed
*.old
*.orig
*~
*.tmp


# START Ruler Generated Files
*.bak
.claude/agents/apex-dev.md
.claude/agents/apex-dev.md.bak
# END Ruler Generated Files
